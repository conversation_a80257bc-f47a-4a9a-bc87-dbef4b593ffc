import { MailService } from '@sendgrid/mail';

if (!process.env.SENDGRID_API_KEY) {
  console.warn("SENDGRID_API_KEY not found. Email functionality will be disabled.");
}

const mailService = new MailService();
if (process.env.SENDGRID_API_KEY) {
  mailService.setApiKey(process.env.SENDGRID_API_KEY);
}

interface EmailParams {
  to: string;
  customerName: string;
  songTitle: string;
  mp3Link: string;
  packageType: string;
}

export async function sendSongDeliveryEmail(params: EmailParams): Promise<boolean> {
  if (!process.env.SENDGRID_API_KEY) {
    console.log('SendGrid not configured. Email would be sent to:', params.to);
    return false;
  }

  try {
    const emailContent = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%); color: #fff; border-radius: 12px; overflow: hidden;">
        <div style="background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%); padding: 20px; text-align: center;">
          <h1 style="margin: 0; color: #000; font-size: 24px; font-weight: bold;">🎵 Your Custom Song is Ready!</h1>
        </div>

        <div style="padding: 30px;">
          <h2 style="color: #fbbf24; margin-bottom: 20px;">Dear ${params.customerName},</h2>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            We're thrilled to deliver your beautiful custom song: <strong style="color: #fbbf24;">"${params.songTitle}"</strong>
          </p>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Your ${params.packageType} package has been completed with love and care. Click the button below to download your personalized song:
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${params.mp3Link}"
               style="background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%); color: #000; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">
              🎵 Download Your Song
            </a>
          </div>

          <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #fbbf24; margin-top: 0;">What's Next?</h3>
            <ul style="margin: 0; padding-left: 20px;">
              <li style="margin-bottom: 8px;">Download and enjoy your custom song</li>
              <li style="margin-bottom: 8px;">Share it with your loved ones</li>
              <li style="margin-bottom: 8px;">Consider leaving us a review to help others</li>
              <li>Reach out if you'd like another custom song!</li>
            </ul>
          </div>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            We'd love to hear how your song made you feel! Please consider sharing your experience:
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <a href="${process.env.REPLIT_DEV_DOMAIN || 'https://yourdomain.com'}/feedback"
               style="background: transparent; color: #fbbf24; padding: 12px 25px; text-decoration: none; border: 2px solid #fbbf24; border-radius: 8px; font-weight: bold; display: inline-block;">
              ⭐ Share Your Feedback
            </a>
          </div>

          <p style="font-size: 14px; color: #999; margin-top: 30px; text-align: center;">
            Thank you for choosing KMStudioPH for your special musical moment!<br>
            Creating memories through music, one song at a time. 🎵
          </p>
        </div>
      </div>
    `;

    await mailService.send({
      to: params.to,
      from: '<EMAIL>', // You'll need to verify this domain with SendGrid
      subject: `🎵 Your Custom Song "${params.songTitle}" is Ready!`,
      html: emailContent,
    });

    console.log(`Song delivery email sent successfully to ${params.to}`);
    return true;
  } catch (error) {
    console.error('Failed to send song delivery email:', error);
    return false;
  }
}

export async function sendGiftCard(params: {
  to: string;
  recipientName: string;
  giverName: string;
  cardTemplate: string;
  cardMessage: string;
  occasion: string;
  packageType: string;
}): Promise<boolean> {
  if (!process.env.SENDGRID_API_KEY) {
    console.log('SendGrid not configured. Gift card would be sent to:', params.to);
    return false;
  }

  const templateColors: Record<string, string> = {
    birthday: "from-pink-500 to-purple-600",
    anniversary: "from-red-500 to-pink-500",
    love: "from-rose-500 to-red-600",
    christmas: "from-green-500 to-red-500",
    graduation: "from-blue-500 to-indigo-600",
    custom: "from-yellow-500 to-orange-500"
  };

  const templateEmojis: Record<string, string> = {
    birthday: "🎂",
    anniversary: "💕",
    love: "❤️",
    christmas: "🎄",
    graduation: "🎓",
    custom: "✨"
  };

  try {
    const emailContent = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; border-radius: 12px; overflow: hidden;">
        <!-- Header -->
        <div style="background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%); padding: 20px; text-align: center;">
          <h1 style="margin: 0; color: #000; font-size: 24px; font-weight: bold;">🎁 You've Received a Musical Gift!</h1>
        </div>

        <!-- Digital Greeting Card -->
        <div style="margin: 30px; background: linear-gradient(135deg, ${templateColors[params.cardTemplate] || templateColors.custom}); border-radius: 12px; padding: 40px; text-align: center; position: relative;">
          <div style="font-size: 48px; margin-bottom: 20px;">${templateEmojis[params.cardTemplate] || templateEmojis.custom}</div>
          <h2 style="color: #fff; font-size: 28px; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
            ${params.occasion}
          </h2>
          <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p style="font-size: 18px; line-height: 1.6; margin: 0; color: #fff;">
              ${params.cardMessage}
            </p>
          </div>
          <p style="font-size: 16px; color: #fff; margin-top: 20px;">
            With love from <strong>${params.giverName}</strong> 💝
          </p>
        </div>

        <!-- Gift Details -->
        <div style="padding: 30px;">
          <h2 style="color: #fbbf24; margin-bottom: 20px;">Dear ${params.recipientName},</h2>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Someone very special has gifted you a custom song from KMStudioPH! 🎵
          </p>

          <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #fbbf24; margin-top: 0;">Your Gift Package:</h3>
            <p style="font-size: 18px; color: #fff; margin: 10px 0;">
              📦 <strong>${params.packageType} Package</strong>
            </p>
            <p style="font-size: 16px; color: #ccc; margin: 5px 0;">
              🎵 A personalized song created just for you
            </p>
            <p style="font-size: 16px; color: #ccc; margin: 5px 0;">
              ⏰ Professional recording and production
            </p>
            <p style="font-size: 16px; color: #ccc; margin: 5px 0;">
              💝 Made with love and care by KMStudioPH
            </p>
          </div>

          <div style="background: #fbbf24; color: #000; padding: 20px; border-radius: 8px; margin: 25px 0; text-align: center;">
            <h3 style="margin-top: 0;">What happens next?</h3>
            <p style="margin: 10px 0;">
              ✨ Our musicians will start creating your personalized song<br>
              📧 You'll receive updates on the progress<br>
              🎵 Your finished song will be delivered via email<br>
              ⭐ You can share feedback to help others discover the magic!
            </p>
          </div>

          <p style="font-size: 14px; color: #999; margin-top: 30px; text-align: center;">
            Thank you for being part of the KMStudioPH family!<br>
            Creating memories through music, one song at a time. 🎵
          </p>
        </div>
      </div>
    `;

    await mailService.send({
      to: params.to,
      from: '<EMAIL>',
      subject: `🎁 ${params.giverName} sent you a musical gift!`,
      html: emailContent,
    });

    console.log(`Gift card sent successfully to ${params.to}`);
    return true;
  } catch (error) {
    console.error('Failed to send gift card:', error);
    return false;
  }
}

export async function sendFeedbackThankYou(email: string, customerName: string): Promise<boolean> {
  if (!process.env.SENDGRID_API_KEY) {
    console.log('SendGrid not configured. Thank you email would be sent to:', email);
    return false;
  }

  try {
    const emailContent = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%); color: #fff; border-radius: 12px; overflow: hidden;">
        <div style="background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%); padding: 20px; text-align: center;">
          <h1 style="margin: 0; color: #000; font-size: 24px; font-weight: bold;">💝 Thank You for Your Feedback!</h1>
        </div>

        <div style="padding: 30px;">
          <h2 style="color: #fbbf24; margin-bottom: 20px;">Dear ${customerName},</h2>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Thank you so much for taking the time to share your experience with KMStudioPH! Your feedback means the world to us and helps us continue creating beautiful, meaningful music for our clients.
          </p>

          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Your review will inspire other customers and help them understand the magic of personalized music. We're grateful to be part of your special moments!
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <p style="font-size: 18px; color: #fbbf24; font-weight: bold;">🎵 Keep the music alive! 🎵</p>
          </div>

          <p style="font-size: 14px; color: #999; margin-top: 30px; text-align: center;">
            With musical gratitude,<br>
            The KMStudioPH Team
          </p>
        </div>
      </div>
    `;

    await mailService.send({
      to: email,
      from: '<EMAIL>',
      subject: '💝 Thank You for Your Feedback - KMStudioPH',
      html: emailContent,
    });

    return true;
  } catch (error) {
    console.error('Failed to send thank you email:', error);
    return false;
  }
}