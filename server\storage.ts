import { songOrders, type SongOrder, type InsertSongOrder, users, type User, type InsertUser, affiliateReferrals, type AffiliateReferral, type InsertAffiliateReferral } from "@shared/schema";
import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import { eq, desc } from "drizzle-orm";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  createSongOrder(order: InsertSongOrder): Promise<SongOrder>;
  getSongOrders(): Promise<SongOrder[]>;
  createAffiliateReferral(referral: InsertAffiliateReferral): Promise<AffiliateReferral>;
  getAffiliateReferrals(): Promise<AffiliateReferral[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private songOrders: Map<number, SongOrder>;
  private affiliateReferrals: Map<number, AffiliateReferral>;
  private currentUserId: number;
  private currentOrderId: number;
  private currentReferralId: number;

  constructor() {
    this.users = new Map();
    this.songOrders = new Map();
    this.affiliateReferrals = new Map();
    this.currentUserId = 1;
    this.currentOrderId = 1;
    this.currentReferralId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async createSongOrder(insertOrder: InsertSongOrder): Promise<SongOrder> {
    const id = this.currentOrderId++;
    const order: SongOrder = {
      ...insertOrder,
      songTitle: insertOrder.songTitle || null,
      mood: insertOrder.mood || null,
      referenceFile: insertOrder.referenceFile || null,
      referralCode: insertOrder.referralCode || null,
      id,
      createdAt: new Date()
    };
    this.songOrders.set(id, order);
    return order;
  }

  async getSongOrders(): Promise<SongOrder[]> {
    return Array.from(this.songOrders.values());
  }

  async createAffiliateReferral(insertReferral: InsertAffiliateReferral): Promise<AffiliateReferral> {
    const id = this.currentReferralId++;
    const referral: AffiliateReferral = {
      ...insertReferral,
      status: insertReferral.status || "pending",
      id,
      createdAt: new Date()
    };
    this.affiliateReferrals.set(id, referral);
    return referral;
  }

  async getAffiliateReferrals(): Promise<AffiliateReferral[]> {
    return Array.from(this.affiliateReferrals.values());
  }
}

// Database storage class
export class DatabaseStorage implements IStorage {
  private db;

  constructor() {
    const sql = neon(process.env.DATABASE_URL!);
    this.db = drizzle(sql);
  }

  async getUser(id: number): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.id, id));
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.username, username));
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await this.db.insert(users).values(insertUser).returning();
    return result[0];
  }

  async createSongOrder(insertOrder: InsertSongOrder): Promise<SongOrder> {
    const result = await this.db.insert(songOrders).values(insertOrder).returning();
    return result[0];
  }

  async getSongOrders(): Promise<SongOrder[]> {
    return await this.db.select().from(songOrders).orderBy(desc(songOrders.createdAt));
  }

  async createAffiliateReferral(insertReferral: InsertAffiliateReferral): Promise<AffiliateReferral> {
    const result = await this.db.insert(affiliateReferrals).values(insertReferral).returning();
    return result[0];
  }

  async getAffiliateReferrals(): Promise<AffiliateReferral[]> {
    return await this.db.select().from(affiliateReferrals).orderBy(desc(affiliateReferrals.createdAt));
  }
}

// Use Google Sheets as primary storage with memory storage as backup
export const storage = new MemStorage();
